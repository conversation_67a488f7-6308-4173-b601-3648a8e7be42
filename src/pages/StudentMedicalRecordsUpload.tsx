import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, TextField, Paper, Grid, CircularProgress, Alert, MenuItem, List, ListItem, ListItemText, IconButton, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { useFirebase } from '../contexts/FirebaseContext';
import { useSupabaseFileUpload } from '../hooks/useSupabaseFileUpload';
import { STORAGE_BUCKETS } from '../services/supabase';
import { v4 as uuidv4 } from 'uuid';
import type { MedicalRecord, FileAttachment } from '../types/medical';
import { createMedicalRecord } from '../services/medicalRecordService';
import { supabase } from '../../services/supabase';
import DownloadIcon from '@mui/icons-material/Download';
import DeleteIcon from '@mui/icons-material/Delete';
import FolderIcon from '@mui/icons-material/Folder';
import CreateNewFolderIcon from '@mui/icons-material/CreateNewFolder';
import { List as FileList, ListItem as FileListItem, ListItemText as FileListItemText, IconButton as FileIconButton, CircularProgress as FileCircularProgress, Typography as FileTypography, Box as FileBox, Breadcrumbs, Link } from '@mui/material';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';

const StudentMedicalRecordsUpload: React.FC = () => {
  const { currentUser } = useFirebase();
  const { uploadState, uploadMedia, resetUploadState } = useSupabaseFileUpload();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState<'diagnosis' | 'treatment' | 'lab_results' | 'imaging' | 'prescription' | 'other'>('other');
  const [uploading, setUploading] = useState(false);
  const [successMsg, setSuccessMsg] = useState('');
  const [errorMsg, setErrorMsg] = useState('');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
      resetUploadState();
    }
  };

  const handleUpload = async () => {
    if (!currentUser || !selectedFile) return;
    setUploading(true);
    setErrorMsg('');
    setSuccessMsg('');
    try {
      // Upload file to Supabase
      await uploadMedia(currentUser.uid, selectedFile, category); // Use category, not 'MEDICAL_RECORDS'
      console.log('UploadState after upload:', uploadState); // Debug: show upload state
      if (!uploadState.filePath || !uploadState.fileUrl) throw new Error('Upload failed');
      // Prepare file attachment object
      const fileAttachment: FileAttachment = {
        id: uuidv4(),
        fileName: uploadState.filePath.split('/').pop() || selectedFile.name,
        originalName: selectedFile.name,
        fileSize: selectedFile.size,
        mimeType: selectedFile.type,
        supabasePath: uploadState.filePath,
        publicUrl: uploadState.fileUrl,
        uploadedBy: currentUser.uid,
        uploadedAt: new Date()
      };
      // Create medical record in Firestore
      const record: Omit<MedicalRecord, 'id' | 'createdAt' | 'updatedAt'> = {
        studentId: currentUser.uid,
        uploadedBy: 'student',
        title,
        description,
        category,
        date: new Date(),
        files: [fileAttachment],
        isPrivate: false,
      };
      console.log('Creating medical record:', record); // Debug: show record being created
      await createMedicalRecord(record);
      setSuccessMsg('Medical record uploaded successfully!');
      setTitle('');
      setDescription('');
      setSelectedFile(null);
    } catch (err: any) {
      setErrorMsg(err.message || 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  // --- Medical Records File Manager ---
  const [files, setFiles] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!currentUser) return;
    setLoading(true);
    // List files from userid/MEDICAL_RECORDS
    const path = `${currentUser.uid}/MEDICAL_RECORDS`;
    console.log('Listing files at:', path); // Debug: show path being listed
    supabase.storage
      .from('medical-records')
      .list(path)
      .then(({ data, error }) => {
        console.log('Files returned from Supabase .list:', data); // Debug: show files array
        console.log('Error from Supabase .list:', error); // Debug: show error if any
        if (!error) setFiles(data || []);
        setLoading(false);
      });
  }, [currentUser]);

  const handleDownload = async (fileName: string) => {
    try {
      const filePath = `${currentUser.uid}/MEDICAL_RECORDS/${fileName}`;
      console.log('Downloading file:', filePath);

      // Try to download the file directly
      const { data, error } = await supabase.storage
        .from('medical-records')
        .download(filePath);

      if (error) {
        console.error('Download error:', error);
        // Fallback to public URL
        const { data: urlData } = supabase.storage
          .from('medical-records')
          .getPublicUrl(filePath);
        window.open(urlData.publicUrl, '_blank');
        return;
      }

      // Create download link
      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error downloading file:', error);
      alert('Failed to download file. Please try again.');
    }
  };

  const handleDelete = async (fileName: string) => {
    const filePath = `${currentUser.uid}/MEDICAL_RECORDS/${fileName}`;
    await supabase.storage
      .from('medical-records')
      .remove([filePath]);
    setFiles(files.filter(f => f.name !== fileName));
  };

  // --- Medical Records File Explorer ---
  const MedicalRecordsFileExplorer = () => {
    const { currentUser } = useFirebase();
    const [path, setPath] = useState<string[]>([]); // e.g. ['userId', 'imaging']
    const [items, setItems] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [createFolderOpen, setCreateFolderOpen] = useState(false);
    const [newFolderName, setNewFolderName] = useState('');

    // Start at user's root folder
    useEffect(() => {
      if (currentUser) setPath([currentUser.uid]);
    }, [currentUser]);

    useEffect(() => {
      if (!currentUser || path.length === 0) return;
      setLoading(true);
      supabase.storage
        .from('medical-records')
        .list(path.join('/'))
        .then(({ data, error }) => {
          setItems(data || []);
          setLoading(false);
        });
    }, [currentUser, path]);

    const handleOpenFolder = (folderName: string) => {
      setPath([...path, folderName]);
    };

    const handleBreadcrumbClick = (idx: number) => {
      setPath(path.slice(0, idx + 1));
    };

    const handleDownload = async (fileName: string) => {
      try {
        const filePath = [...path, fileName].join('/');
        console.log('Downloading file:', filePath);

        // Try to download the file directly
        const { data, error } = await supabase.storage
          .from('medical-records')
          .download(filePath);

        if (error) {
          console.error('Download error:', error);
          // Fallback to public URL
          const { data: urlData } = supabase.storage
            .from('medical-records')
            .getPublicUrl(filePath);
          window.open(urlData.publicUrl, '_blank');
          return;
        }

        // Create download link
        const url = window.URL.createObjectURL(data);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

      } catch (error) {
        console.error('Error downloading file:', error);
        alert('Failed to download file. Please try again.');
      }
    };

    const handleDelete = async (fileName: string) => {
      const filePath = [...path, fileName].join('/');
      await supabase.storage.from('medical-records').remove([filePath]);
      setItems(items.filter(item => item.name !== fileName));
    };

    const handleCreateFolder = async (folderName: string) => {
      try {
        // Create a placeholder file in the new folder to ensure it exists
        const folderPath = [...path, folderName, '.placeholder'].join('/');
        const placeholderFile = new File([''], '.placeholder', { type: 'text/plain' });

        const { error } = await supabase.storage
          .from('medical-records')
          .upload(folderPath, placeholderFile);

        if (error) {
          console.error('Error creating folder:', error);
          alert('Failed to create folder. Please try again.');
          return;
        }

        // Refresh the file list
        const { data, error: listError } = await supabase.storage
          .from('medical-records')
          .list(path.join('/'));

        if (!listError) {
          setItems(data || []);
        }

        console.log('Folder created successfully:', folderName);
      } catch (error) {
        console.error('Error creating folder:', error);
        alert('Failed to create folder. Please try again.');
      }
    };

    const handleCreateFolderSubmit = () => {
      if (newFolderName.trim()) {
        handleCreateFolder(newFolderName.trim());
        setNewFolderName('');
        setCreateFolderOpen(false);
      }
    };

    return (
      <FileBox sx={{ mt: 4 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          {path.map((segment, idx) => (
            <Link
              key={idx}
              underline="hover"
              color={idx === path.length - 1 ? 'text.primary' : 'inherit'}
              onClick={() => handleBreadcrumbClick(idx)}
              sx={{ cursor: 'pointer' }}
            >
              {segment}
            </Link>
          ))}
        </Breadcrumbs>

        <Box sx={{ mb: 2 }}>
          <Button
            variant="outlined"
            startIcon={<CreateNewFolderIcon />}
            onClick={() => setCreateFolderOpen(true)}
            size="small"
          >
            Create Folder
          </Button>
        </Box>

        {loading ? <FileCircularProgress /> : (
          <FileList>
            {items.length === 0 && <FileTypography>No files or folders found.</FileTypography>}
            {items.map(item =>
              item.metadata ? (
                // File
                <FileListItem key={item.id || item.name}
                  secondaryAction={
                    <>
                      <FileIconButton onClick={() => handleDownload(item.name)}><DownloadIcon /></FileIconButton>
                      <FileIconButton onClick={() => handleDelete(item.name)}><DeleteIcon /></FileIconButton>
                    </>
                  }
                >
                  <InsertDriveFileIcon sx={{ mr: 1 }} />
                  <FileListItemText primary={item.name} />
                </FileListItem>
              ) : (
                // Folder
                <FileListItem key={item.name} button onClick={() => handleOpenFolder(item.name)}>
                  <FolderIcon sx={{ mr: 1 }} />
                  <FileListItemText primary={item.name} />
                </FileListItem>
              )
            )}
          </FileList>
        )}

        {/* Create Folder Dialog */}
        <Dialog open={createFolderOpen} onClose={() => setCreateFolderOpen(false)}>
          <DialogTitle>Create New Folder</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Folder Name"
              fullWidth
              variant="outlined"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleCreateFolderSubmit();
                }
              }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateFolderOpen(false)}>Cancel</Button>
            <Button onClick={handleCreateFolderSubmit} variant="contained">
              Create
            </Button>
          </DialogActions>
        </Dialog>
      </FileBox>
    );
  };

  return (
    <Paper sx={{ p: 4, maxWidth: 600, mx: 'auto', mt: 4 }}>
      <Typography variant="h5" gutterBottom>Upload Medical Record</Typography>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField label="Title" value={title} onChange={e => setTitle(e.target.value)} fullWidth required />
        </Grid>
        <Grid item xs={12}>
          <TextField label="Description" value={description} onChange={e => setDescription(e.target.value)} fullWidth multiline rows={2} />
        </Grid>
        <Grid item xs={12}>
          <TextField select label="Category" value={category} onChange={e => setCategory(e.target.value as any)} fullWidth SelectProps={{ native: true }}>
            <option value="diagnosis">Diagnosis</option>
            <option value="treatment">Treatment</option>
            <option value="lab_results">Lab Results</option>
            <option value="imaging">Imaging</option>
            <option value="prescription">Prescription</option>
            <option value="other">Other</option>
          </TextField>
        </Grid>
        <Grid item xs={12}>
          <Button variant="contained" component="label" fullWidth>
            {selectedFile ? selectedFile.name : 'Select File'}
            <input type="file" hidden onChange={handleFileChange} />
          </Button>
        </Grid>
        <Grid item xs={12}>
          <Button variant="contained" color="primary" onClick={handleUpload} disabled={!selectedFile || !title || uploading} fullWidth>
            {uploading ? <CircularProgress size={24} /> : 'Upload'}
          </Button>
        </Grid>
        {uploadState.isUploading && <Grid item xs={12}><CircularProgress /></Grid>}
        {successMsg && <Grid item xs={12}><Alert severity="success">{successMsg}</Alert></Grid>}
        {errorMsg && <Grid item xs={12}><Alert severity="error">{errorMsg}</Alert></Grid>}
      </Grid>
      {/* --- Medical Records File Manager --- */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>My Medical Records</Typography>
        {/* Removed category selector since all files are in the same folder */}
        {loading ? <CircularProgress /> : (
          <List>
            {files.length === 0 && <Typography>No files found in this location.</Typography>}
            {files.map(file => (
              <ListItem key={file.id || file.name} secondaryAction={
                <>
                  <IconButton onClick={() => handleDownload(file.name)}><DownloadIcon /></IconButton>
                  <IconButton onClick={() => handleDelete(file.name)}><DeleteIcon /></IconButton>
                </>
              }>
                <ListItemText primary={file.name} />
              </ListItem>
            ))}
          </List>
        )}
      </Box>
      <MedicalRecordsFileExplorer />
    </Paper>
  );
};

export default StudentMedicalRecordsUpload;
