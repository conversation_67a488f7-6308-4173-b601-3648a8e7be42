import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Paper, Chip, Divider, 
  Button, IconButton, Avatar, Grid, Breadcrumbs
} from '@mui/material';
import { 
  ArrowBack as ArrowBackIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Share as ShareIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import { useNavigate, useParams, Link } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import { getHealthResourceById } from '../../services/healthResourcesService';

const HealthResourceDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [resource, setResource] = useState<any>(null);
  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    if (!id) return;
    // Check if resource is saved
    const savedResources = JSON.parse(localStorage.getItem('savedHealthResources') || '[]');
    setIsSaved(savedResources.includes(id));

    setLoading(true);
    getHealthResourceById(id)
      .then((res) => {
        setResource(res);
      })
      .catch(() => setResource(null))
      .finally(() => setLoading(false));
  }, [id]);
  
  const toggleSave = () => {
    const savedResources = JSON.parse(localStorage.getItem('savedHealthResources') || '[]');
    let updatedSaved;
    
    if (isSaved) {
      updatedSaved = savedResources.filter(resourceId => resourceId !== id);
    } else {
      updatedSaved = [...savedResources, id];
    }
    
    localStorage.setItem('savedHealthResources', JSON.stringify(updatedSaved));
    setIsSaved(!isSaved);
  };
  
  if (loading) {
    return (
      <Layout>
        <Container maxWidth="md" sx={{ py: 4 }}>
          <Typography>Loading...</Typography>
        </Container>
      </Layout>
    );
  }
  if (!resource) {
    return (
      <Layout>
        <Container maxWidth="md" sx={{ py: 4 }}>
          <Typography color="error">Resource not found.</Typography>
        </Container>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 2 }}>
          <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
            Home
          </Link>
          <Link to="/health-resources" style={{ textDecoration: 'none', color: 'inherit' }}>
            Health Resources
          </Link>
          <Typography color="text.primary">{resource.title}</Typography>
        </Breadcrumbs>
        
        {/* Back Button */}
        <Button 
          startIcon={<ArrowBackIcon />} 
          onClick={() => navigate('/health-resources')}
          sx={{ mb: 3 }}
        >
          Back to Resources
        </Button>
        
        {/* Article Header */}
        <Paper 
          sx={{ 
            p: 4, 
            mb: 4, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            {resource.title}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Chip 
              label={resource.category} 
              color="primary" 
              size="small" 
              sx={{ mr: 2 }}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
              {resource.publishDate}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {resource.readTime}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar 
                src={resource.authorImage}
                alt={resource.author}
                sx={{ mr: 2 }}
              />
              <Box>
                <Typography variant="subtitle1" fontWeight="medium">
                  {resource.author}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {resource.authorTitle}
                </Typography>
              </Box>
            </Box>
            
            <Box>
              <IconButton 
                onClick={toggleSave}
                color={isSaved ? 'primary' : 'default'}
                aria-label={isSaved ? 'Unsave resource' : 'Save resource'}
              >
                {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}
              </IconButton>
              <IconButton aria-label="Share resource">
                <ShareIcon />
              </IconButton>
              <IconButton aria-label="Print resource">
                <PrintIcon />
              </IconButton>
            </Box>
          </Box>
        </Paper>
        
        {/* Featured Image */}
        <Box 
          sx={{ 
            mb: 4, 
            borderRadius: 3,
            overflow: 'hidden',
            height: { xs: '200px', sm: '300px', md: '400px' },
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <img 
            src={resource.imageUrl} 
            alt={resource.title}
            style={{ 
              width: '100%', 
              height: '100%', 
              objectFit: 'cover' 
            }}
          />
        </Box>
        
        {/* Article Content */}
        <Grid container spacing={4}>
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 4, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="subtitle1" fontWeight="medium" paragraph>
                {resource.summary}
              </Typography>
              
              <Divider sx={{ my: 3 }} />
              
              <Box 
                sx={{ 
                  '& h2': {
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    mt: 4,
                    mb: 2
                  },
                  '& h3': {
                    fontSize: '1.25rem',
                    fontWeight: 'bold',
                    mt: 3,
                    mb: 1.5
                  },
                  '& p': {
                    mb: 2,
                    lineHeight: 1.7
                  },
                  '& ul, & ol': {
                    pl: 4,
                    mb: 2
                  },
                  '& li': {
                    mb: 1
                  }
                }}
                dangerouslySetInnerHTML={{ __html: resource.content }}
              />
              
              <Divider sx={{ my: 3 }} />
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 3 }}>
                {resource.tags.map((tag, index) => (
                  <Chip 
                    key={index} 
                    label={tag} 
                    size="small" 
                    variant="outlined"
                    sx={{ mb: 1 }}
                  />
                ))}
              </Box>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                mb: 4, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Related Resources
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <img 
                    src="https://source.unsplash.com/random/100x100/?anxiety" 
                    alt="Anxiety Management"
                    style={{ 
                      width: 80, 
                      height: 80, 
                      objectFit: 'cover',
                      borderRadius: 8
                    }}
                  />
                  <Box>
                    <Typography variant="subtitle2" fontWeight="medium">
                      Anxiety Management Techniques
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Dr. Emily Wilson
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <img 
                    src="https://source.unsplash.com/random/100x100/?meditation" 
                    alt="Meditation Basics"
                    style={{ 
                      width: 80, 
                      height: 80, 
                      objectFit: 'cover',
                      borderRadius: 8
                    }}
                  />
                  <Box>
                    <Typography variant="subtitle2" fontWeight="medium">
                      Meditation Basics for Beginners
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Dr. Michael Chen
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <img 
                    src="https://source.unsplash.com/random/100x100/?sleep" 
                    alt="Sleep Hygiene"
                    style={{ 
                      width: 80, 
                      height: 80, 
                      objectFit: 'cover',
                      borderRadius: 8
                    }}
                  />
                  <Box>
                    <Typography variant="subtitle2" fontWeight="medium">
                      Improving Sleep Hygiene
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Dr. Sarah Johnson
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Paper>
            
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Need Help?
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Typography variant="body2" paragraph>
                If you're experiencing mental health concerns, don't hesitate to reach out for support.
              </Typography>
              
              <Button 
                variant="contained" 
                color="primary" 
                fullWidth
                sx={{ mb: 2, borderRadius: 2 }}
                onClick={() => navigate('/appointments')}
              >
                Book an Appointment
              </Button>
              
              <Button 
                variant="outlined" 
                fullWidth
                sx={{ borderRadius: 2 }}
                onClick={() => navigate('/support-resources')}
              >
                View Support Resources
              </Button>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default HealthResourceDetail;
