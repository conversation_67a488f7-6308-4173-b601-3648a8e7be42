// Supabase Configuration and Client
// Handles file storage, database operations, and real-time subscriptions

import { createClient } from '@supabase/supabase-js';
import { auth } from './firebase'; // Import Firebase auth for user verification

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'your-supabase-url';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Storage bucket names
export const STORAGE_BUCKETS = {
  MEDICAL_RECORDS: 'medical-records',
  HEALTH_METRICS: 'health-metrics',
  PROFILE_IMAGES: 'profile-images',
  HEALTH_TIPS_MEDIA: 'health-tips-media'
} as const;

// Removed automatic bucket creation logic. Buckets must be created manually in the Supabase dashboard.

/**
 * Upload file to Supabase storage
 */
export const uploadFile = async (
  bucketName: string,
  filePath: string,
  file: File,
  options?: {
    cacheControl?: string;
    contentType?: string;
    upsert?: boolean;
  }
) => {
  try {
    console.log(`Uploading file to bucket: ${bucketName}, path: ${filePath}`);

    // Skip authentication check for public bucket uploads
    console.log('Proceeding with file upload to public bucket...');

    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        cacheControl: options?.cacheControl || '3600',
        contentType: options?.contentType || file.type,
        upsert: options?.upsert || true // Default to true for health resources
      });

    if (error) {
      console.error('Supabase upload error:', error);
      throw new Error(`Upload failed: ${error.message} (${error.statusCode || 'Unknown status'})`);
    }

    console.log('File uploaded successfully:', data);
    return data;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
};

/**
 * Download file from Supabase storage with enhanced error handling
 */
export const downloadFile = async (bucketName: string, filePath: string) => {
  try {
    console.log(`Attempting to download file from bucket: ${bucketName}, path: ${filePath}`);

    const { data, error } = await supabase.storage
      .from(bucketName)
      .download(filePath);

    if (error) {
      console.error('Supabase storage error:', error);
      throw new Error(`Download failed: ${error.message}`);
    }

    if (!data) {
      throw new Error('No data received from Supabase storage');
    }

    console.log('File downloaded successfully, size:', data.size);
    return data;
  } catch (error) {
    console.error('Error downloading file:', error);
    throw error;
  }
};

/**
 * Download file with fallback to signed URL
 */
export const downloadFileWithFallback = async (bucketName: string, filePath: string) => {
  try {
    // First try direct download
    return await downloadFile(bucketName, filePath);
  } catch (error) {
    console.warn('Direct download failed, trying signed URL approach:', error);

    try {
      // Fallback to signed URL
      const signedUrl = await createSignedUrl(bucketName, filePath, 300); // 5 minutes

      const response = await fetch(signedUrl);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.blob();
    } catch (fallbackError) {
      console.error('Signed URL fallback also failed:', fallbackError);
      throw new Error(`Both download methods failed. Original error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
};

/**
 * Get public URL for a file
 */
export const getFileUrl = (bucketName: string, filePath: string) => {
  const { data } = supabase.storage
    .from(bucketName)
    .getPublicUrl(filePath);

  return data.publicUrl;
};

/**
 * Delete file from Supabase storage
 */
export const deleteFile = async (bucketName: string, filePath: string) => {
  try {
    const { error } = await supabase.storage
      .from(bucketName)
      .remove([filePath]);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
};

/**
 * List files in a storage bucket
 */
export const listFiles = async (
  bucketName: string,
  folderPath?: string,
  options?: {
    limit?: number;
    offset?: number;
    sortBy?: { column: string; order: 'asc' | 'desc' };
  }
) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .list(folderPath, {
        limit: options?.limit || 100,
        offset: options?.offset || 0,
        sortBy: options?.sortBy || { column: 'name', order: 'asc' }
      });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error listing files:', error);
    throw error;
  }
};

/**
 * Create signed URL for private file access
 */
export const createSignedUrl = async (
  bucketName: string,
  filePath: string,
  expiresIn: number = 3600 // 1 hour default
) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      throw error;
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Error creating signed URL:', error);
    throw error;
  }
};

/**
 * Generate unique file path for uploads
 */
export const generateFilePath = (
  userId: string,
  category: string,
  fileName: string
): string => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');

  return `${userId}/${category}/${timestamp}_${sanitizedFileName}`;
};

/**
 * Validate file before upload
 */
export const validateFile = (
  file: File,
  options?: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
  }
): { isValid: boolean; error?: string } => {
  const maxSize = options?.maxSize || 10485760; // 10MB default
  const allowedTypes = options?.allowedTypes || [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit`
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'File type not allowed'
    };
  }

  return { isValid: true };
};

/**
 * Check if user is authenticated for storage access using Firebase Auth
 */
export const checkStorageAuth = async () => {
  try {
    const firebaseUser = auth.currentUser;

    if (!firebaseUser) {
      console.error('Auth check error: No Firebase user found');
      return { authenticated: false, error: 'Auth session missing!' };
    }

    // Verify the user's token is still valid
    try {
      await firebaseUser.getIdToken(true); // Force refresh to ensure token is valid
    } catch (tokenError) {
      console.error('Token validation failed:', tokenError);
      return {
        authenticated: false,
        error: 'Auth session expired. Please log in again.'
      };
    }

    return {
      authenticated: true,
      user: {
        id: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName
      },
      error: null
    };
  } catch (error) {
    console.error('Auth check failed:', error);
    return {
      authenticated: false,
      error: error instanceof Error ? error.message : 'Unknown auth error'
    };
  }
};

/**
 * Initialize storage bucket with proper policies
 */
export const initializeHealthTipsBucket = async () => {
  try {
    console.log('Checking health-tips-media bucket...');

    // Check if bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();

    if (listError) {
      console.error('Error listing buckets:', listError);
      return { success: false, error: listError.message };
    }

    const healthTipsBucket = buckets?.find(bucket => bucket.name === STORAGE_BUCKETS.HEALTH_TIPS_MEDIA);

    if (!healthTipsBucket) {
      console.log('Health-tips-media bucket not found, but this is expected if it was created manually in Supabase dashboard');
      console.log('Skipping bucket creation - assuming bucket exists and is configured properly');
      return { success: true, error: null };
    } else {
      console.log('Health-tips-media bucket found and accessible');
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error initializing health tips bucket:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Get file with authentication check
 */
export const getAuthenticatedFile = async (bucketName: string, filePath: string) => {
  // Check authentication first
  const authCheck = await checkStorageAuth();

  if (!authCheck.authenticated) {
    throw new Error(`Authentication required for file access. ${authCheck.error || ''}`);
  }

  console.log('User authenticated, proceeding with file download...');
  return await downloadFileWithFallback(bucketName, filePath);
};

/**
 * Debug function to test file access and provide detailed information
 */
export const debugFileAccess = async (bucketName: string, filePath: string) => {
  console.log('=== FILE ACCESS DEBUG ===');
  console.log('Bucket:', bucketName);
  console.log('File Path:', filePath);

  try {
    // Check authentication
    const authCheck = await checkStorageAuth();
    console.log('Auth Status:', authCheck);

    // Try to list files in the bucket to check access
    try {
      const fileList = await listFiles(bucketName, filePath.split('/').slice(0, -1).join('/'));
      console.log('Bucket accessible, files found:', fileList?.length || 0);
    } catch (listError) {
      console.log('Bucket list error:', listError);
    }

    // Try to get public URL
    try {
      const publicUrl = getFileUrl(bucketName, filePath);
      console.log('Public URL:', publicUrl);
    } catch (urlError) {
      console.log('Public URL error:', urlError);
    }

    // Try to create signed URL
    try {
      const signedUrl = await createSignedUrl(bucketName, filePath, 300);
      console.log('Signed URL created successfully');

      // Test the signed URL
      const response = await fetch(signedUrl);
      console.log('Signed URL response:', response.status, response.statusText);
    } catch (signedError) {
      console.log('Signed URL error:', signedError);
    }

  } catch (error) {
    console.log('Debug error:', error);
  }

  console.log('=== END DEBUG ===');
};

/**
 * Test health tips media bucket access - simplified version
 */
export const testHealthTipsBucketAccess = async () => {
  console.log('=== HEALTH TIPS BUCKET TEST ===');

  try {
    // Check authentication using Firebase (for logging only)
    const authCheck = await checkStorageAuth();
    console.log('Firebase Auth Status:', authCheck);

    // Check bucket existence
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    console.log('Available buckets:', buckets?.map(b => b.name));

    if (listError) {
      console.error('Error listing buckets:', listError);
      return { success: false, error: listError.message };
    }

    const healthBucket = buckets?.find(b => b.name === STORAGE_BUCKETS.HEALTH_TIPS_MEDIA);
    console.log('Health tips bucket found:', !!healthBucket);

    if (!healthBucket) {
      console.error('Health tips media bucket not found!');
      return { success: false, error: 'Bucket not found' };
    }

    // Test file upload (using a simple path that doesn't require user ID)
    const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
    const testPath = `test/test-${Date.now()}.txt`;

    console.log('Testing file upload to:', testPath);

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(STORAGE_BUCKETS.HEALTH_TIPS_MEDIA)
      .upload(testPath, testFile, { upsert: true });

    if (uploadError) {
      console.error('Upload test failed:', uploadError);
      return { success: false, error: uploadError.message };
    }

    console.log('Upload test successful:', uploadData);

    // Clean up test file
    try {
      await supabase.storage
        .from(STORAGE_BUCKETS.HEALTH_TIPS_MEDIA)
        .remove([testPath]);
      console.log('Test file cleaned up successfully');
    } catch (cleanupError) {
      console.warn('Failed to clean up test file:', cleanupError);
    }

    console.log('=== BUCKET TEST PASSED ===');
    return { success: true, error: null };

  } catch (error) {
    console.error('Bucket test error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Simple test function to verify bucket access
 * Call this from browser console: window.testSupabaseBucket()
 */
export const testSupabaseBucket = async () => {
  console.log('Testing Supabase bucket access...');
  try {
    const result = await testHealthTipsBucketAccess();
    console.log('Test result:', result);
    return result;
  } catch (error) {
    console.error('Test failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Make test function available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).testSupabaseBucket = testSupabaseBucket;
}

// Initialize buckets on module load
// initializeStorageBuckets();

export default supabase;
