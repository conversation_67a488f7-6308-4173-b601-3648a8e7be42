// Doctor Assignment Service
// Manages the assignment and retrieval of doctors for students

import { collection, getDocs, doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from './firebase';
import { getAllUsers } from './authService';

export interface Doctor {
  id: string;
  name: string;
  specialty: string;
  department: string;
  avatar?: string;
  online?: boolean;
  assigned?: boolean;
  requested?: boolean;
  email?: string;
  uid?: string;
}

export interface AssignedDoctor extends Doctor {
  assignedDate: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
}

// Core specialties that every student should have access to
const CORE_SPECIALTIES = ['General Practice', 'Mental Health', 'Emergency Medicine'];

/**
 * Get all doctors from Firebase
 */
export const getDoctorsFromFirebase = async (): Promise<Doctor[]> => {
  try {
    const allUsers = await getAllUsers();
    const doctors = allUsers
      .filter(user => user.role === 'doctor')
      .filter(user => user.uid) // Ensure user has a valid UID
      .map(user => ({
        id: user.uid, // Primary ID
        uid: user.uid, // Backup ID
        name: user.displayName || user.email?.split('@')[0] || 'Unknown Doctor',
        specialty: user.specialty || 'General Practice',
        department: user.department || 'Primary Care',
        email: user.email,
        avatar: '',
        online: false, // You can update this with real presence logic if needed
        assigned: false,
        requested: false
      }));

    console.log(`Found ${doctors.length} doctors with valid IDs`);

    // Debug: Log doctors without proper names
    const doctorsWithoutNames = doctors.filter(d => d.name === 'Unknown Doctor');
    if (doctorsWithoutNames.length > 0) {
      console.warn(`Found ${doctorsWithoutNames.length} doctors without proper names:`, doctorsWithoutNames);
    }

    return doctors;
  } catch (error) {
    console.error('Error fetching doctors from Firebase:', error);
    return [];
  }
};

/**
 * Smart doctor assignment algorithm
 * Ensures every student gets essential care coverage
 */
export const assignDoctorsToStudent = async (studentId: string): Promise<AssignedDoctor[]> => {
  try {
    const availableDoctors = await getDoctorsFromFirebase();
    if (availableDoctors.length === 0) {
      return [];
    }

    // If we have fewer than 5 doctors total, assign all of them
    if (availableDoctors.length <= 5) {
      const assignedDoctors = formatAssignedDoctors(availableDoctors);
      await saveAssignedDoctors(studentId, assignedDoctors);
      return assignedDoctors;
    }

    // Load balancing: Get current assignment counts for each doctor
    const doctorAssignmentCounts = await getDoctorAssignmentCounts(availableDoctors);

    // Step 1: Assign core specialists (essential for all students)
    const coreAssignments = CORE_SPECIALTIES.map(specialty => {
      const specialistDoctors = availableDoctors.filter(d => d.specialty === specialty);
      if (specialistDoctors.length > 0) {
        // Load balancing: pick the doctor with the least assignments
        return specialistDoctors.reduce((least, current) =>
          doctorAssignmentCounts[current.id] < doctorAssignmentCounts[least.id] ? current : least
        );
      }
      return null;
    }).filter((d): d is Doctor => d !== null); // filter out nulls

    // Step 2: Assign additional doctors for comprehensive care
    const remainingDoctors = availableDoctors.filter(d =>
      !coreAssignments.some(core => core && core.id === d.id)
    );

    const additionalCount = Math.max(0, 5 - coreAssignments.length); // Aim for 5 total doctors

    // Sort remaining doctors by assignment count (ascending) for load balancing
    const sortedRemainingDoctors = remainingDoctors.sort((a, b) =>
      doctorAssignmentCounts[a.id] - doctorAssignmentCounts[b.id]
    );

    const additionalDoctors = sortedRemainingDoctors.slice(0, additionalCount);

    // Step 3: Combine assignments
    const allAssignments = [...coreAssignments, ...additionalDoctors];
    // Step 4: Format and save assignments
    const assignedDoctors = formatAssignedDoctors(allAssignments);

    // Step 5: Save to Firestore
    await saveAssignedDoctors(studentId, assignedDoctors);

    return assignedDoctors;

  } catch (error) {
    console.error('Error assigning doctors:', error);
    return [];
  }
};

/**
 * Format doctors as AssignedDoctor objects
 */
const formatAssignedDoctors = (doctors: Doctor[]): AssignedDoctor[] => {
  return doctors.map((doctor) => ({
    ...doctor,
    assignedDate: new Date().toISOString(),
    assigned: true
    // No mock lastMessage, lastMessageTime, unreadCount
  }));
};

/**
 * Save assigned doctors to Firestore
 */
const saveAssignedDoctors = async (studentId: string, doctors: AssignedDoctor[]): Promise<void> => {
  try {
    await setDoc(doc(db, 'doctorAssignments', studentId), {
      doctors: doctors.map(doctor => ({
        id: doctor.id,
        uid: doctor.uid,
        name: doctor.name,
        specialty: doctor.specialty,
        department: doctor.department,
        email: doctor.email,
        assignedDate: doctor.assignedDate,
        lastMessage: doctor.lastMessage,
        lastMessageTime: doctor.lastMessageTime,
        unreadCount: doctor.unreadCount
      })),
      updatedAt: new Date().toISOString()
    });
    console.log(`💾 Saved doctor assignments to Firestore for student: ${studentId}`);
  } catch (error) {
    console.error('Error saving doctor assignments:', error);
    // No fallback to localStorage
  }
};

/**
 * Get assigned doctors for a student
 */
export const getAssignedDoctors = async (studentId: string): Promise<AssignedDoctor[]> => {
  try {
    const docRef = doc(db, 'doctorAssignments', studentId);
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      const data = docSnap.data();
      return data.doctors || [];
    }
    return [];
  } catch (error) {
    console.error('Error fetching assigned doctors:', error);
    return [];
  }
};

/**
 * Get doctor assignment counts for load balancing
 */
const getDoctorAssignmentCounts = async (doctors: Doctor[]): Promise<Record<string, number>> => {
  try {
    const assignmentCounts: Record<string, number> = {};
    doctors.forEach(doctor => {
      assignmentCounts[doctor.id] = 0;
    });
    const assignmentsSnapshot = await getDocs(collection(db, 'doctorAssignments'));
    assignmentsSnapshot.forEach(docSnap => {
      const data = docSnap.data();
      if (data.doctors && Array.isArray(data.doctors)) {
        (data.doctors as { id: string }[]).forEach((assignedDoctor) => {
          if (Object.prototype.hasOwnProperty.call(assignmentCounts, assignedDoctor.id)) {
            assignmentCounts[assignedDoctor.id]++;
          }
        });
      }
    });
    return assignmentCounts;
  } catch (error) {
    console.error('Error getting doctor assignment counts:', error);
    const emptyCounts: Record<string, number> = {};
    doctors.forEach(doctor => {
      emptyCounts[doctor.id] = 0;
    });
    return emptyCounts;
  }
};

/**
 * Get all available doctors (for browsing)
 */
export const getAllDoctors = async (): Promise<Doctor[]> => {
  try {
    const doctors = await getDoctorsFromFirebase();
    return doctors.map(doctor => ({
      ...doctor,
      assigned: false,
      requested: false
    }));
  } catch (error) {
    console.error('Error fetching all doctors:', error);
    return [];
  }
};

/**
 * Request assignment of a new doctor
 */
export const requestDoctorAssignment = async (studentId: string, doctorId: string): Promise<boolean> => {
  try {
    // TODO: Use studentId to create a real assignment request in Firestore in the future
    const doctors = await getDoctorsFromFirebase();
    const doctor = doctors.find(d => d.id === doctorId);
    if (!doctor) {
      return false;
    }
    // Optionally, create a Firestore document for the request here
    // ...
    return true;
  } catch (error) {
    console.error('Error requesting doctor assignment:', error);
    return false;
  }
};

/**
 * Get assignment statistics for admin dashboard
 */
export const getAssignmentStats = () => {
  return {
    totalDoctors: 0,
    totalStudents: 0,
    avgAssignmentsPerStudent: 0,
    totalAssignments: 0,
    coreSpecialtiesCoverage: '0%',
    mostRequestedSpecialty: '',
    leastRequestedSpecialty: ''
  };
};
